#!/usr/bin/env python3
"""
Test script for Robust Balance Validation System
Tests the new robust validation functionality for Historical vs Momentum balance ratios
"""

import sys
import traceback
from datetime import datetime

def test_robust_balance_validation():
    """Test the robust balance validation system"""
    print("🧪 Testing Robust Balance Validation System")
    print("=" * 60)
    
    try:
        # Import the enhanced learning system
        from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem, RobustBalanceValidator
        print("✅ Successfully imported enhanced learning system")
        
        # Create enhanced learning system instance
        enhanced_system = EnhancedAdaptiveLearningSystem()
        print("✅ Successfully created enhanced learning system instance")
        
        # Check data availability
        completed_predictions = [p for p in enhanced_system.contextual_predictions if p.actual_winner is not None]
        print(f"📊 Data Status:")
        print(f"   Total predictions: {len(enhanced_system.contextual_predictions)}")
        print(f"   Completed predictions: {len(completed_predictions)}")
        print(f"   Required minimum: 150")
        
        if len(completed_predictions) < 10:
            print("\n⚠️ WARNING: Very limited data available for testing")
            print("   This test will demonstrate the validation framework")
            print("   but results may not be statistically meaningful")
        
        # Test the robust balance validator directly
        print(f"\n🔬 Testing RobustBalanceValidator class...")
        validator = RobustBalanceValidator()
        print("✅ Successfully created RobustBalanceValidator instance")
        
        # Test configuration
        config = validator.config
        print(f"📋 Validation Configuration:")
        print(f"   Cross-validation splits: {config.n_splits}")
        print(f"   Bootstrap samples: {config.bootstrap_samples}")
        print(f"   Historical ratios to test: {config.historical_ratios}")
        print(f"   Minimum context sample size: {config.min_context_sample_size}")
        print(f"   Significance threshold: {config.significance_threshold}")
        
        # Run the validation (this will handle insufficient data gracefully)
        print(f"\n🚀 Running robust balance validation...")
        validation_results = enhanced_system.run_robust_balance_validation()
        
        # Display results
        print(f"\n📊 VALIDATION RESULTS:")
        print(f"   Status: {validation_results.get('status', 'unknown')}")
        
        if validation_results.get('status') == 'insufficient_data':
            print(f"   ⚠️ Insufficient data for full validation")
            print(f"   Completed predictions: {validation_results.get('completed_predictions', 0)}")
            print(f"   This is expected for new installations")
            
        elif validation_results.get('status') == 'operation_denied':
            print(f"   🛡️ Operation denied by coordinator")
            print(f"   Reason: {validation_results.get('message', 'Unknown')}")
            
        elif validation_results.get('status') == 'validation_error':
            print(f"   ❌ Validation error occurred")
            print(f"   Error: {validation_results.get('error', 'Unknown')}")
            
        else:
            # Successful validation
            overall_summary = validation_results.get('overall_summary', {})
            print(f"   ✅ Validation completed successfully!")
            print(f"   Overall accuracy: {overall_summary.get('overall_accuracy', 0.0):.3f}")
            print(f"   Contexts tested: {overall_summary.get('contexts_tested', 0)}")
            print(f"   Significant contexts: {overall_summary.get('statistically_significant_contexts', 0)}")
            
            # Show optimal ratios if available
            optimal_ratios = overall_summary.get('optimal_ratios_by_context', {})
            if optimal_ratios:
                print(f"\n🎯 Optimal Balance Ratios:")
                for context, ratio_info in optimal_ratios.items():
                    hist_ratio = ratio_info['historical_ratio']
                    mom_ratio = ratio_info['momentum_ratio']
                    accuracy = ratio_info['accuracy']
                    print(f"   {context}: {hist_ratio:.1f}/{mom_ratio:.1f} (acc: {accuracy:.3f})")
        
        print(f"\n✅ Test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all required modules are available")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print(f"\n🔍 Full traceback:")
        traceback.print_exc()
        return False

def test_gui_integration():
    """Test GUI integration (dashboard button)"""
    print(f"\n🖥️ Testing GUI Integration")
    print("=" * 40)
    
    try:
        # Test if enhanced learning dashboard can import the new functionality
        from enhanced_learning_dashboard import ValidationWidget
        print("✅ Successfully imported ValidationWidget from enhanced_learning_dashboard")

        # Check if the new button method exists
        if hasattr(ValidationWidget, 'run_balance_validation'):
            print("✅ run_balance_validation method found in ValidationWidget")
        else:
            print("❌ run_balance_validation method not found in ValidationWidget")
            return False
        
        print("✅ GUI integration test passed!")
        return True
        
    except ImportError as e:
        print(f"❌ GUI import error: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI test error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 ROBUST BALANCE VALIDATION SYSTEM TEST")
    print("=" * 80)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run tests
    test1_passed = test_robust_balance_validation()
    test2_passed = test_gui_integration()
    
    # Summary
    print(f"\n" + "=" * 80)
    print(f"📋 TEST SUMMARY:")
    print(f"   Core validation system: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   GUI integration: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   The Robust Balance Validation System is ready for use!")
        print(f"   Access it through the Enhanced Learning Dashboard.")
    else:
        print(f"\n⚠️ SOME TESTS FAILED")
        print(f"   Please review the errors above and fix any issues.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
