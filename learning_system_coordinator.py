"""
Learning System Coordinator
Manages coordination between Enhanced Learning System and V2 Learning System
Prevents conflicts and ensures optimal learning progression
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import threading
from pathlib import Path

class SystemState(Enum):
    """States for learning systems"""
    IDLE = "idle"
    OPTIMIZING = "optimizing"
    VALIDATING = "validating"
    ACCURACY_DROP_PROTECTION = "accuracy_drop_protection"
    COOLDOWN = "cooldown"

class OptimizationType(Enum):
    """Types of optimization operations"""
    BALANCE_OPTIMIZATION = "balance_optimization"
    WEIGHT_VALIDATION = "weight_validation"
    EMERGENCY_REBALANCE = "emergency_rebalance"

@dataclass
class SystemOperation:
    """Represents a system operation request"""
    system_name: str
    operation_type: OptimizationType
    priority: int  # 1=highest, 10=lowest
    requested_at: datetime
    estimated_duration: int  # minutes
    requires_exclusive_access: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AccuracyMonitor:
    """Monitors accuracy trends and triggers protection"""
    recent_accuracies: List[float] = field(default_factory=list)
    window_size: int = 50  # Last 50 predictions
    drop_threshold: float = 0.05  # 5% drop triggers protection
    protection_duration: int = 1000  # predictions to wait before allowing changes
    last_protection_triggered: Optional[datetime] = None
    
class LearningSystemCoordinator:
    """
    Central coordinator for all learning systems
    Prevents conflicts and manages optimization scheduling
    """
    
    def __init__(self):
        self.state_file = Path("learning_coordinator_state.json")
        self.lock = threading.Lock()
        
        # System states
        self.enhanced_system_state = SystemState.IDLE
        self.v2_system_state = SystemState.IDLE
        
        # Operation queue
        self.operation_queue: List[SystemOperation] = []
        self.current_operation: Optional[SystemOperation] = None
        
        # Accuracy monitoring
        self.accuracy_monitor = AccuracyMonitor()
        
        # Configuration
        self.config = {
            'min_improvement_threshold': 0.02,  # 2% minimum improvement
            'statistical_significance_threshold': 0.05,  # p-value threshold
            'balance_change_cooldown': 500,  # predictions between balance changes
            'validation_respect_period': 300,  # predictions to respect validated weights
            'emergency_accuracy_threshold': 0.10,  # 10% drop triggers emergency mode
        }
        
        # State tracking
        self.last_balance_change: Optional[datetime] = None
        self.last_validation: Optional[datetime] = None
        self.predictions_since_last_balance_change = 0
        self.predictions_since_last_validation = 0
        
        # Load existing state
        self.load_state()
    
    def request_operation(self, operation: SystemOperation) -> bool:
        """
        Request permission to perform a learning operation
        Returns True if operation is approved, False if denied
        """
        with self.lock:
            # Check if accuracy drop protection is active
            if self._is_accuracy_protection_active():
                if operation.operation_type != OptimizationType.EMERGENCY_REBALANCE:
                    print(f"🛡️ Accuracy drop protection active - denying {operation.operation_type.value}")
                    return False
            
            # Check cooldown periods
            if not self._check_cooldown_periods(operation):
                return False
            
            # Check system availability
            if not self._is_system_available(operation):
                print(f"⏸️ System busy - queueing {operation.operation_type.value}")
                self._queue_operation(operation)
                return False
            
            # Approve operation
            self._start_operation(operation)
            return True
    
    def complete_operation(self, operation: SystemOperation, 
                          success: bool, results: Dict[str, Any] = None):
        """Mark an operation as complete and update state"""
        with self.lock:
            if self.current_operation and self.current_operation == operation:
                self.current_operation = None
                
                # Log system change for monitoring
                try:
                    from learning_performance_monitor import performance_monitor
                    performance_monitor.log_system_change(
                        system_name=operation.system_name,
                        change_type=operation.operation_type.value,
                        details={
                            'success': success,
                            'results': results or {},
                            'duration_seconds': (datetime.now() - operation.requested_at).total_seconds(),
                            'metadata': operation.metadata
                        },
                        triggered_by="coordinator"
                    )
                except ImportError:
                    pass

                # Update tracking based on operation type
                if operation.operation_type == OptimizationType.BALANCE_OPTIMIZATION:
                    self.last_balance_change = datetime.now()
                    self.predictions_since_last_balance_change = 0
                    self.enhanced_system_state = SystemState.COOLDOWN

                elif operation.operation_type == OptimizationType.WEIGHT_VALIDATION:
                    self.last_validation = datetime.now()
                    self.predictions_since_last_validation = 0
                    self.v2_system_state = SystemState.COOLDOWN
                
                # Log operation completion
                self._log_operation_completion(operation, success, results)
                
                # Process next operation in queue
                self._process_queue()
                
                # Save state
                self.save_state()
    
    def update_accuracy(self, new_accuracy: float):
        """Update accuracy monitoring and trigger protection if needed"""
        with self.lock:
            self.accuracy_monitor.recent_accuracies.append(new_accuracy)
            
            # Keep only recent accuracies
            if len(self.accuracy_monitor.recent_accuracies) > self.accuracy_monitor.window_size:
                self.accuracy_monitor.recent_accuracies.pop(0)
            
            # Log accuracy measurement
            try:
                from learning_performance_monitor import performance_monitor
                performance_monitor.log_accuracy_measurement(
                    accuracy=new_accuracy,
                    sample_size=self.predictions_since_last_balance_change,
                    context="coordinator_update"
                )
            except ImportError:
                pass

            # Check for accuracy drop
            if len(self.accuracy_monitor.recent_accuracies) >= 10:  # Need minimum data
                recent_avg = sum(self.accuracy_monitor.recent_accuracies[-10:]) / 10
                older_avg = sum(self.accuracy_monitor.recent_accuracies[-20:-10]) / 10 if len(self.accuracy_monitor.recent_accuracies) >= 20 else recent_avg

                accuracy_drop = older_avg - recent_avg

                if accuracy_drop > self.accuracy_monitor.drop_threshold:
                    # Log the accuracy drop event
                    try:
                        from learning_performance_monitor import performance_monitor
                        performance_monitor.log_event(
                            event_type="coordinator_accuracy_drop",
                            description=f"Coordinator detected accuracy drop: {older_avg:.3f} -> {recent_avg:.3f}",
                            severity="warning",
                            metadata={
                                'older_accuracy': older_avg,
                                'recent_accuracy': recent_avg,
                                'drop_amount': accuracy_drop,
                                'predictions_count': self.predictions_since_last_balance_change
                            }
                        )
                    except ImportError:
                        pass

                    self._trigger_accuracy_protection(accuracy_drop)
    
    def increment_prediction_counters(self):
        """Increment prediction counters for cooldown tracking"""
        with self.lock:
            self.predictions_since_last_balance_change += 1
            self.predictions_since_last_validation += 1
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        with self.lock:
            return {
                'enhanced_system_state': self.enhanced_system_state.value,
                'v2_system_state': self.v2_system_state.value,
                'current_operation': self.current_operation.operation_type.value if self.current_operation else None,
                'queue_length': len(self.operation_queue),
                'accuracy_protection_active': self._is_accuracy_protection_active(),
                'predictions_since_last_balance_change': self.predictions_since_last_balance_change,
                'predictions_since_last_validation': self.predictions_since_last_validation,
                'recent_accuracy_trend': self._get_accuracy_trend(),
                'next_allowed_operations': self._get_next_allowed_operations()
            }
    
    def _is_accuracy_protection_active(self) -> bool:
        """Check if accuracy drop protection is currently active"""
        if not self.accuracy_monitor.last_protection_triggered:
            return False
        
        time_since_protection = datetime.now() - self.accuracy_monitor.last_protection_triggered
        predictions_since_protection = self.predictions_since_last_balance_change + self.predictions_since_last_validation
        
        return predictions_since_protection < self.accuracy_monitor.protection_duration
    
    def _check_cooldown_periods(self, operation: SystemOperation) -> bool:
        """Check if operation violates cooldown periods"""
        if operation.operation_type == OptimizationType.BALANCE_OPTIMIZATION:
            if self.predictions_since_last_balance_change < self.config['balance_change_cooldown']:
                print(f"⏳ Balance change cooldown active ({self.predictions_since_last_balance_change}/{self.config['balance_change_cooldown']})")
                return False
        
        # Check validation respect period
        if (operation.operation_type == OptimizationType.BALANCE_OPTIMIZATION and 
            self.predictions_since_last_validation < self.config['validation_respect_period']):
            print(f"🔒 Validation respect period active ({self.predictions_since_last_validation}/{self.config['validation_respect_period']})")
            return False
        
        return True
    
    def _is_system_available(self, operation: SystemOperation) -> bool:
        """Check if the required system is available"""
        if operation.requires_exclusive_access and self.current_operation:
            return False
        
        if operation.operation_type == OptimizationType.BALANCE_OPTIMIZATION:
            return self.enhanced_system_state in [SystemState.IDLE, SystemState.COOLDOWN]
        elif operation.operation_type == OptimizationType.WEIGHT_VALIDATION:
            return self.v2_system_state in [SystemState.IDLE, SystemState.COOLDOWN]
        
        return True
    
    def _queue_operation(self, operation: SystemOperation):
        """Add operation to queue with priority ordering"""
        self.operation_queue.append(operation)
        self.operation_queue.sort(key=lambda x: (x.priority, x.requested_at))
    
    def _start_operation(self, operation: SystemOperation):
        """Start an approved operation"""
        self.current_operation = operation
        
        if operation.operation_type == OptimizationType.BALANCE_OPTIMIZATION:
            self.enhanced_system_state = SystemState.OPTIMIZING
        elif operation.operation_type == OptimizationType.WEIGHT_VALIDATION:
            self.v2_system_state = SystemState.VALIDATING
        
        print(f"🚀 Starting {operation.operation_type.value} for {operation.system_name}")
    
    def _process_queue(self):
        """Process next operation in queue if possible"""
        if not self.operation_queue or self.current_operation:
            return
        
        next_operation = self.operation_queue[0]
        if self._is_system_available(next_operation) and self._check_cooldown_periods(next_operation):
            self.operation_queue.pop(0)
            self._start_operation(next_operation)
    
    def _trigger_accuracy_protection(self, accuracy_drop: float):
        """Trigger accuracy drop protection mode"""
        self.accuracy_monitor.last_protection_triggered = datetime.now()
        print(f"🛡️ ACCURACY DROP PROTECTION ACTIVATED - Drop: {accuracy_drop:.3f}")
        print(f"   Blocking non-emergency optimizations for {self.accuracy_monitor.protection_duration} predictions")
    
    def _get_accuracy_trend(self) -> Dict[str, Any]:
        """Get current accuracy trend analysis"""
        if len(self.accuracy_monitor.recent_accuracies) < 10:
            return {'status': 'insufficient_data'}
        
        recent_10 = self.accuracy_monitor.recent_accuracies[-10:]
        recent_avg = sum(recent_10) / len(recent_10)
        
        if len(self.accuracy_monitor.recent_accuracies) >= 20:
            older_10 = self.accuracy_monitor.recent_accuracies[-20:-10]
            older_avg = sum(older_10) / len(older_10)
            trend = recent_avg - older_avg
        else:
            trend = 0
        
        return {
            'recent_average': recent_avg,
            'trend': trend,
            'status': 'improving' if trend > 0.01 else 'declining' if trend < -0.01 else 'stable'
        }
    
    def _get_next_allowed_operations(self) -> List[str]:
        """Get list of operations that would be allowed right now"""
        allowed = []
        
        test_operations = [
            SystemOperation("enhanced", OptimizationType.BALANCE_OPTIMIZATION, 5, datetime.now(), 10),
            SystemOperation("v2", OptimizationType.WEIGHT_VALIDATION, 5, datetime.now(), 15)
        ]
        
        for op in test_operations:
            if (not self._is_accuracy_protection_active() and 
                self._check_cooldown_periods(op) and 
                self._is_system_available(op)):
                allowed.append(op.operation_type.value)
        
        return allowed
    
    def _log_operation_completion(self, operation: SystemOperation, 
                                 success: bool, results: Dict[str, Any]):
        """Log operation completion for debugging"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'operation': operation.operation_type.value,
            'system': operation.system_name,
            'success': success,
            'results': results or {}
        }
        
        # Could save to file for debugging
        print(f"📝 Operation completed: {operation.operation_type.value} - {'✅' if success else '❌'}")
    
    def save_state(self):
        """Save coordinator state to file"""
        state = {
            'last_balance_change': self.last_balance_change.isoformat() if self.last_balance_change else None,
            'last_validation': self.last_validation.isoformat() if self.last_validation else None,
            'predictions_since_last_balance_change': self.predictions_since_last_balance_change,
            'predictions_since_last_validation': self.predictions_since_last_validation,
            'accuracy_monitor': {
                'recent_accuracies': self.accuracy_monitor.recent_accuracies,
                'last_protection_triggered': self.accuracy_monitor.last_protection_triggered.isoformat() if self.accuracy_monitor.last_protection_triggered else None
            }
        }
        
        with open(self.state_file, 'w') as f:
            json.dump(state, f, indent=2)
    
    def load_state(self):
        """Load coordinator state from file"""
        if not self.state_file.exists():
            return
        
        try:
            with open(self.state_file, 'r') as f:
                state = json.load(f)
            
            if state.get('last_balance_change'):
                self.last_balance_change = datetime.fromisoformat(state['last_balance_change'])
            if state.get('last_validation'):
                self.last_validation = datetime.fromisoformat(state['last_validation'])
            
            self.predictions_since_last_balance_change = state.get('predictions_since_last_balance_change', 0)
            self.predictions_since_last_validation = state.get('predictions_since_last_validation', 0)
            
            if 'accuracy_monitor' in state:
                self.accuracy_monitor.recent_accuracies = state['accuracy_monitor'].get('recent_accuracies', [])
                if state['accuracy_monitor'].get('last_protection_triggered'):
                    self.accuracy_monitor.last_protection_triggered = datetime.fromisoformat(
                        state['accuracy_monitor']['last_protection_triggered']
                    )
        
        except Exception as e:
            print(f"⚠️ Could not load coordinator state: {e}")

# Global coordinator instance
learning_coordinator = LearningSystemCoordinator()
